#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules/which/bin/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules/which/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/which@2.0.2/node_modules:/home/<USER>/Desktop/Projects/assignment/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/node-which" "$@"
else
  exec node  "$basedir/../../bin/node-which" "$@"
fi
