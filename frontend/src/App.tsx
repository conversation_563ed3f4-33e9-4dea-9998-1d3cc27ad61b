import { useState, useEffect } from 'react';
import './App.css';
import PingWindow from './components/PingWindow';
import type { Ping } from './types/ping';

// Hardcoded messages for demonstration
const hardcodedMessages: Ping[] = [
  {
    id: '1',
    text: 'Hey there! How are you doing?',
    ts: Date.now() - 300000, // 5 minutes ago
    fromMe: false
  },
  {
    id: '2',
    text: 'I\'m doing great, thanks for asking! How about you?',
    ts: Date.now() - 240000,
    fromMe: true
  },
  {
    id: '3',
    text: 'Pretty good! Just working on some React components.',
    ts: Date.now() - 180000,
    fromMe: false
  },
  {
    id: '4',
    text: 'That sounds interesting! What kind of components?',
    ts: Date.now() - 120000,
    fromMe: true
  },
  {
    id: '5',
    text: 'A chat-like ping window component. It has auto-scrolling and different styles for incoming vs outgoing messages.',
    ts: Date.now() - 60000,
    fromMe: false
  },
  {
    id: '6',
    text: 'Cool! That sounds like a useful component. Does it handle real-time updates?',
    ts: Date.now() - 30000,
    fromMe: true
  },
  {
    id: '7',
    text: 'Yes! It automatically scrolls to the bottom when new messages arrive, but only if the user is already near the bottom.',
    ts: Date.now() - 10000,
    fromMe: false
  }
];

function App() {
  const [pings, setPings] = useState<Ping[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Simulate loading messages progressively to demonstrate auto-scroll
  useEffect(() => {
    let isMounted = true; // Flag to prevent double loading in StrictMode

    const loadMessages = async () => {
      if (!isMounted) return;

      setIsLoading(true);
      for (let i = 0; i < hardcodedMessages.length; i++) {
        if (!isMounted) break; // Check if component is still mounted
        await new Promise(resolve => setTimeout(resolve, 300)); // Reduced delay
        setPings(prev => [...prev, hardcodedMessages[i]]);
      }
      setIsLoading(false);
    };

    loadMessages();

    // Cleanup function to prevent double loading
    return () => {
      isMounted = false;
    };
  }, []);

  // Function to send a new message
  const sendMessage = () => {
    if (inputMessage.trim() === '') return;

    const newMessage: Ping = {
      id: `msg-${Date.now()}`,
      text: inputMessage.trim(),
      ts: Date.now(),
      fromMe: true
    };

    setPings(prev => [...prev, newMessage]);
    setInputMessage(''); // Clear input after sending
  };

  // Handle Enter key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="app">
      <div className="app-header">
        <h1>Ping Window Demo</h1>
        <p>A production-ready React component with chat interface</p>
      </div>

      <div className="app-content">
        <PingWindow pings={pings} />

        <div className="chat-input-container">
          <div className="chat-input-wrapper">
            <input
              type="text"
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Type your message..."
              className="chat-input"
              disabled={isLoading}
            />
            <button
              onClick={sendMessage}
              disabled={inputMessage.trim() === '' || isLoading}
              className="send-button"
              aria-label="Send message"
            >
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
              >
                <line x1="22" y1="2" x2="11" y2="13"></line>
                <polygon points="22,2 15,22 11,13 2,9"></polygon>
              </svg>
            </button>
          </div>
          <p className="message-count">
            Total messages: {pings.length}
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
