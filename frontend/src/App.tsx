import { useState, useEffect } from 'react';
import './App.css';
import PingWindow from './components/PingWindow';
import type { Ping } from './types/ping';

// Hardcoded messages for demonstration
const hardcodedMessages: Ping[] = [
  {
    id: '1',
    text: 'Hey there! How are you doing?',
    ts: Date.now() - 300000, // 5 minutes ago
    fromMe: false
  },
  {
    id: '2',
    text: 'I\'m doing great, thanks for asking! How about you?',
    ts: Date.now() - 240000, 
    fromMe: true
  },
  {
    id: '3',
    text: 'Pretty good! Just working on some React components.',
    ts: Date.now() - 180000, 
    fromMe: false
  },
  {
    id: '4',
    text: 'That sounds interesting! What kind of components?',
    ts: Date.now() - 120000,
    fromMe: true
  },
  {
    id: '5',
    text: 'A chat-like ping window component. It has auto-scrolling and different styles for incoming vs outgoing messages.',
    ts: Date.now() - 60000, 
    fromMe: false
  },
  {
    id: '6',
    text: 'Cool! That sounds like a useful component. Does it handle real-time updates?',
    ts: Date.now() - 30000, 
    fromMe: true
  },
  {
    id: '7',
    text: 'Yes! It automatically scrolls to the bottom when new messages arrive, but only if the user is already near the bottom.',
    ts: Date.now() - 10000, 
    fromMe: false
  }
];

function App() {
  const [pings, setPings] = useState<Ping[]>([]);

  // Simulate loading messages progressively to demonstrate auto-scroll
  useEffect(() => {
    const loadMessages = async () => {
      for (let i = 0; i < hardcodedMessages.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay between messages
        setPings(prev => [...prev, hardcodedMessages[i]]);
      }
    };

    loadMessages();
  }, []);

  // Function to add a new message 
  const addNewMessage = () => {
    const newMessage: Ping = {
      id: `new-${Date.now()}`,
      text: 'This is a new message added dynamically!',
      ts: Date.now(),
      fromMe: Math.random() > 0.5 // Randomly assign sender
    };
    setPings(prev => [...prev, newMessage]);
  };

  return (
    <div className="app">
      <div className="app-header">
        <h1>Ping Window Demo</h1>
      </div>

      <div className="app-content">
        <PingWindow pings={pings} />

        <div className="app-controls">
          <button
            onClick={addNewMessage}
            className="add-message-btn"
          >
            Add New Message
          </button>
          <p className="message-count">
            Total messages: {pings.length}
          </p>
        </div>
      </div>
    </div>
  );
}

export default App;
